# Generated by Django 4.2.1 on 2025-07-10 06:30
import django.core.validators
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("ai_agent", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="aiagent",
            name="temperature",
            field=models.FloatField(
                default=0.7,
                help_text="Controls randomness in the output. Lower values make output more focused and deterministic.",
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(2.0),
                ],
            ),
        ),
    ]
