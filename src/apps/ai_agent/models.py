from apps.base.models import BaseTimedModel
from apps.base.models import BaseTrackableModel
from apps.base.models import OrganizationScope
from apps.secret.models import Secret
from apps.secret.models import SecretTypeEnum
from apps.users.auth_utils import get_current_authenticated_user
from apps.users.authentication import RemoteUser
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator
from django.core.validators import MinValueValidator
from django.db import models
from django.db import transaction


class AIAgentSecretManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(secret_type=SecretTypeEnum.AI_AGENT)


class AIAgentSecret(Secret):
    objects = AIAgentSecretManager()

    class Meta:
        proxy = True

    def save(self, *args, **kwargs):
        self.secret_type = SecretTypeEnum.AI_AGENT
        return super().save(*args, **kwargs)


class AIProviderFormType(models.TextChoices):
    API_KEY = "api_key", "API key"
    ORG_API_KEY = "org_api_key", "Organization API key"


class AIProvider(models.Model):
    name = models.CharField(max_length=120, unique=True)
    title = models.CharField(max_length=120, unique=True)
    form_type = models.CharField(max_length=64, choices=AIProviderFormType.choices)


class AIModel(models.Model):
    name = models.CharField(max_length=120)
    provider = models.ForeignKey(AIProvider, on_delete=models.CASCADE)


class ResponseTypeEnum(models.TextChoices):
    TEXT = "text", "Text"
    JSON_SCHEMA = "json_schema", "JSON Schema"


class AIAgent(BaseTimedModel, BaseTrackableModel, OrganizationScope):
    name = models.CharField(max_length=120)
    description = models.TextField(max_length=500, blank=True, null=True)
    provider = models.ForeignKey(AIProvider, on_delete=models.CASCADE)
    model = models.CharField(max_length=256)
    secret = models.ForeignKey(AIAgentSecret, on_delete=models.SET_NULL, null=True)
    instructions = models.TextField(max_length=4000, blank=True, null=True)
    max_tokens = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(100000000)], default=10000
    )
    timeout = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(1800)], default=700
    )
    max_retries = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(10)], default=5
    )
    temperature = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(2.0)],
        default=0.7,
        help_text="Controls randomness in the output. Lower values make output more focused and deterministic.",
    )
    response_type = models.CharField(
        max_length=20,
        choices=ResponseTypeEnum.choices,  # noqa
        default=ResponseTypeEnum.TEXT,
    )
    response_schema = models.JSONField(null=True, blank=True)
    tools = models.JSONField(null=True, blank=True)

    class Meta:
        unique_together = ("name", "team_id")

    def __str__(self):
        return f"{self.name}"

    def save(self, *args, **kwargs):
        with transaction.atomic():
            if not self.id:
                remote_user: RemoteUser = get_current_authenticated_user()
                agents_limit = 200
                if AIAgent.objects.filter(team_id=remote_user.team_id).count() > (
                    agents_limit - 1
                ):
                    raise ValidationError(
                        f"You can’t connect a new model because you’ve reached"
                        f" the limit of {agents_limit} connections per team."
                    )
            return super().save(*args, **kwargs)
