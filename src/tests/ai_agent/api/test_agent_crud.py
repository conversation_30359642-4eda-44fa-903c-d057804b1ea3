import copy
from unittest.mock import Mock
from unittest.mock import patch

from apps.ai_agent.models import AIProvider
from apps.base.enums import AuthTypeEnum
from apps.users.authentication import Remote<PERSON>ser
from rest_framework import status as http_status
from rest_framework.test import APITestCase
from tests.workflow.api.base_api_client import APIClient
from tests.workflow.api.base_api_client import path_to

TEAM_ID = 6085

auth_user_mock = Mock()
remote_user = RemoteUser(
    **{"user_id": 6085, "team_id": TEAM_ID, "auth_type": AuthTypeEnum.PROXY}
)
auth_user_mock.return_value = remote_user


class TestAIAgent(APITestCase):
    client_class = APIClient

    def setUp(self):
        # Create test provider
        self.provider = AIProvider.objects.get(
            name="openai",
        )
        self.anthropic_provider = AIProvider.objects.get(
            name="anthropic",
        )

        self.AGENT_DATA = {
            "name": "test-ai-agent",
            "model": "test-ai-agent",
            "description": "AI agent for testing",
            "provider": self.provider.id,
            "instructions": "You are a helpful assistant",
            "max_tokens": 2000,
            "timeout": 300,
            "max_retries": 3,
            "response_type": "json_schema",
            "response_schema": {"type": "string"},
            "tools": [
                {"type": "function", "function": {"name": "get_current_weather"}}
            ],
        }

    @patch("apps.workflow.models.get_current_authenticated_user", auth_user_mock)
    def test_create(self):
        response = self.client.post(
            path=path_to("ai_agent/agents"), data=self.AGENT_DATA, format="json"
        )
        assert response.status_code == http_status.HTTP_201_CREATED
        assert response.data["created_at"]
        assert response.data["name"] == self.AGENT_DATA["name"]
        assert response.data["description"] == self.AGENT_DATA["description"]
        assert response.data["provider"] == self.AGENT_DATA["provider"]
        assert response.data["instructions"] == self.AGENT_DATA["instructions"]
        assert response.data["max_tokens"] == self.AGENT_DATA["max_tokens"]
        assert response.data["timeout"] == self.AGENT_DATA["timeout"]
        assert response.data["max_retries"] == self.AGENT_DATA["max_retries"]
        assert response.data["response_type"] == self.AGENT_DATA["response_type"]
        assert response.data["response_schema"] == self.AGENT_DATA["response_schema"]
        assert response.data["tools"] == self.AGENT_DATA["tools"]
        assert (
            response.data["updated_by"]
            == response.data["created_by"]
            == str(remote_user.user_id)
        )

    @patch("apps.workflow.models.get_current_authenticated_user", auth_user_mock)
    def test_list(self):
        # Create an agent first
        self.client.post(
            path=path_to("ai_agent/agents"), data=self.AGENT_DATA, format="json"
        )

        # List agents
        response = self.client.get(path=path_to("ai_agent/agents"))
        assert response.status_code == http_status.HTTP_200_OK
        assert len(response.data["results"]) >= 1
        assert response.data["results"][0]["name"] == self.AGENT_DATA["name"]

    @patch("apps.workflow.models.get_current_authenticated_user", auth_user_mock)
    def test_retrieve(self):
        # Create an agent first
        create_response = self.client.post(
            path=path_to("ai_agent/agents"), data=self.AGENT_DATA, format="json"
        )
        agent_id = create_response.data["id"]

        # Retrieve the agent
        response = self.client.get(path=path_to(f"ai_agent/agents/{agent_id}"))
        assert response.status_code == http_status.HTTP_200_OK
        assert response.data["name"] == self.AGENT_DATA["name"]
        assert response.data["provider"] == self.AGENT_DATA["provider"]

    @patch("apps.workflow.models.get_current_authenticated_user", auth_user_mock)
    def test_update(self):
        # Create an agent first
        create_response = self.client.post(
            path=path_to("ai_agent/agents"), data=self.AGENT_DATA, format="json"
        )
        agent_id = create_response.data["id"]

        # Update the agent
        updated_data = copy.deepcopy(self.AGENT_DATA)
        updated_data["name"] = "updated-ai-agent"
        updated_data["provider"] = self.anthropic_provider.id
        updated_data["instructions"] = "Updated instructions"

        response = self.client.patch(
            path=path_to(f"ai_agent/agents/{agent_id}"),
            data=updated_data,
            format="json",
        )
        assert response.status_code == http_status.HTTP_200_OK
        assert response.data["name"] == updated_data["name"]
        assert response.data["provider"] == updated_data["provider"]
        assert response.data["instructions"] == updated_data["instructions"]

    @patch("apps.workflow.models.get_current_authenticated_user", auth_user_mock)
    def test_delete(self):
        # Create an agent first
        create_response = self.client.post(
            path=path_to("ai_agent/agents"), data=self.AGENT_DATA, format="json"
        )
        agent_id = create_response.data["id"]

        # Delete the agent
        response = self.client.delete(path=path_to(f"ai_agent/agents/{agent_id}"))
        assert response.status_code == http_status.HTTP_204_NO_CONTENT

        # Verify it's deleted
        response = self.client.get(path=path_to(f"ai_agent/agents/{agent_id}"))
        assert response.status_code == http_status.HTTP_404_NOT_FOUND

    @patch("apps.workflow.models.get_current_authenticated_user", auth_user_mock)
    def test_create_invalid_provider(self):
        invalid_data = copy.deepcopy(self.AGENT_DATA)
        invalid_data["provider"] = 999  # Invalid provider ID
        response = self.client.post(
            path=path_to("ai_agent/agents"), data=invalid_data, format="json"
        )
        assert response.status_code == http_status.HTTP_400_BAD_REQUEST

    @patch("apps.workflow.models.get_current_authenticated_user", auth_user_mock)
    def test_search_by_name(self):
        # Create an agent
        self.client.post(
            path=path_to("ai_agent/agents"), data=self.AGENT_DATA, format="json"
        )

        # Search by name
        response = self.client.get(path=path_to("ai_agent/agents?search=test-ai"))
        assert response.status_code == http_status.HTTP_200_OK
        assert len(response.data["results"]) >= 1
        assert all(
            "test-ai" in agent["name"].lower() for agent in response.data["results"]
        )
